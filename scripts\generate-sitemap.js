import fs from 'fs';
import path from 'path';

const DOMAIN = 'https://ainotes.work';
const SUPPORTED_LANGUAGES = ['en', 'es', 'fr', 'de', 'zh'];

// 页面配置
const PAGES = [
  {
    path: '/',
    priority: '1.0',
    changefreq: 'daily',
    hasImage: true
  },
  {
    path: '/privacy-policy',
    priority: '0.5',
    changefreq: 'monthly'
  },
  {
    path: '/terms-of-service',
    priority: '0.5',
    changefreq: 'monthly'
  },
  {
    path: '/contact-us',
    priority: '0.7',
    changefreq: 'monthly'
  }
];

function generateHreflangLinks(path) {
  const links = [];
  
  // 为每种语言生成链接
  SUPPORTED_LANGUAGES.forEach(lang => {
    const href = lang === 'en' ? `${DOMAIN}${path}` : `${DOMAIN}/${lang}${path}`;
    const hreflang = getHreflangCode(lang);
    links.push(`    <xhtml:link rel="alternate" hreflang="${hreflang}" href="${href}"/>`);
  });
  
  // 添加 x-default
  links.push(`    <xhtml:link rel="alternate" hreflang="x-default" href="${DOMAIN}${path}"/>`);
  
  return links.join('\n');
}

function getHreflangCode(lang) {
  const hreflangMap = {
    'en': 'en-US',
    'es': 'es-ES',
    'fr': 'fr-FR',
    'de': 'de-DE',
    'zh': 'zh-CN',
  };
  return hreflangMap[lang] || 'en-US';
}

function generateImageTags(path) {
  if (path === '/') {
    return `    <image:image>
      <image:loc>${DOMAIN}/ainotes-logo-512.png</image:loc>
      <image:title>AI Notes Logo</image:title>
      <image:caption>AI Notes - Free Online Note Taking App</image:caption>
    </image:image>`;
  }
  return '';
}

function generateUrlEntry(page, lang = null) {
  const isDefault = lang === null || lang === 'en';
  const url = isDefault ? `${DOMAIN}${page.path}` : `${DOMAIN}/${lang}${page.path}`;
  const lastmod = new Date().toISOString().split('T')[0] + 'T10:00:00+00:00';
  
  const priority = isDefault ? page.priority : (parseFloat(page.priority) * 0.9).toFixed(1);
  
  return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${priority}</priority>
${generateHreflangLinks(page.path)}
${page.hasImage && isDefault ? generateImageTags(page.path) : ''}
  </url>`;
}

function generateSitemap() {
  const header = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">`;

  const footer = `</urlset>`;

  const urls = [];

  // 为每个页面生成URL条目
  PAGES.forEach(page => {
    // 默认版本（英文，无前缀）
    urls.push(generateUrlEntry(page));
    
    // 其他语言版本
    SUPPORTED_LANGUAGES.forEach(lang => {
      if (lang !== 'en') {
        urls.push(generateUrlEntry(page, lang));
      }
    });
  });

  return `${header}

${urls.join('\n\n')}

${footer}`;
}

function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Disallow private or sensitive areas
Disallow: /api/
Disallow: /dist/
Disallow: /node_modules/
Disallow: /*.json$
Disallow: /*.ts$
Disallow: /*.tsx$

# Allow important files
Allow: /sitemap.xml
Allow: /favicon.ico
Allow: /*.png$
Allow: /*.jpg$
Allow: /*.jpeg$
Allow: /*.gif$
Allow: /*.svg$
Allow: /*.css$
Allow: /*.js$

# Sitemap location
Sitemap: ${DOMAIN}/sitemap.xml

# Crawl delay (optional, helps with server load)
Crawl-delay: 1`;
}

function generateManifest() {
  return {
    "name": "AI Notes - Free Online Note Taking App",
    "short_name": "AI Notes",
    "description": "Powerful free online note-taking application with rich text editing, auto-save, and multi-language support",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#ffffff",
    "theme_color": "#ffffff",
    "orientation": "portrait-primary",
    "scope": "/",
    "lang": "en-US",
    "dir": "ltr",
    "categories": ["productivity", "utilities", "education"],
    "icons": [
      {
        "src": "/favicon.ico",
        "sizes": "16x16 32x32 48x48",
        "type": "image/x-icon"
      },
      {
        "src": "/ainotes-logo-192.png",
        "sizes": "192x192",
        "type": "image/png",
        "purpose": "any maskable"
      },
      {
        "src": "/ainotes-logo-512.png",
        "sizes": "512x512",
        "type": "image/png",
        "purpose": "any maskable"
      }
    ],
    "screenshots": [
      {
        "src": "/ainotes-logo-512.png",
        "sizes": "512x512",
        "type": "image/png",
        "platform": "wide",
        "label": "AI Notes Main Interface"
      }
    ],
    "shortcuts": [
      {
        "name": "New Note",
        "short_name": "New",
        "description": "Create a new note",
        "url": "/?action=new",
        "icons": [
          {
            "src": "/ainotes-logo-192.png",
            "sizes": "192x192",
            "type": "image/png"
          }
        ]
      },
      {
        "name": "Recent Notes",
        "short_name": "Recent",
        "description": "View recently edited notes",
        "url": "/?action=recent",
        "icons": [
          {
            "src": "/ainotes-logo-192.png",
            "sizes": "192x192",
            "type": "image/png"
          }
        ]
      }
    ],
    "related_applications": [],
    "prefer_related_applications": false,
    "edge_side_panel": {
      "preferred_width": 400
    },
    "launch_handler": {
      "client_mode": "navigate-existing"
    }
  };
}

// 生成文件
const outputDir = path.join(process.cwd(), 'client', 'public');

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 生成 sitemap.xml
const sitemap = generateSitemap();
fs.writeFileSync(path.join(outputDir, 'sitemap.xml'), sitemap, 'utf8');
console.log('✅ Generated sitemap.xml');

// 生成 robots.txt
const robots = generateRobotsTxt();
fs.writeFileSync(path.join(outputDir, 'robots.txt'), robots, 'utf8');
console.log('✅ Generated robots.txt');

// 生成 manifest.json
const manifest = generateManifest();
fs.writeFileSync(path.join(outputDir, 'manifest.json'), JSON.stringify(manifest, null, 2), 'utf8');
console.log('✅ Generated manifest.json');

console.log('🎉 All SEO files generated successfully!');
